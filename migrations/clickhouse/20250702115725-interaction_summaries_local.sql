CREATE TABLE IF NOT EXISTS goteacher.interaction_summaries_local ON CLUSTER '{cluster_multiple_shard}' (    
    `id` UUID DEFAULT generateUUIDv4(),
    `sessionId` UUID,
    `userId` UUID,
    `tabId` UUID,
    `ip` String DEFAULT '0.0.0.0',
    
    `orgId` UUID,
    `schoolId` UUID,
    `grade` String DEFAULT '',
    `role` String DEFAULT '',
    `schoolYear` String DEFAULT '',
    
    `startTime` DateTime,
    `endTime` DateTime,
    `duration` UInt32, -- in seconds (usually 30)
    `clientTimezone` String DEFAULT timeZone(),
    `serverTimestamp` DateTime DEFAULT now(),
        
    `domain` String DEFAULT '',
    `fullDomain` String DEFAULT '',
    `url` String DEFAULT '',
    `productId` String DEFAULT '',

    `eventName` Enum8('INTERACTION_SUMMARY' = 1, 'IDLE_PING' = 2) DEFAULT 'IDLE_PING',    
    `clickCount` UInt32 DEFAULT 0,
    `scrollEventCount` UInt32 DEFAULT 0,
    `keypressCount` UInt32 DEFAULT 0,
    `mouseMoveDuration` UInt32 DEFAULT 0, -- in milliseconds or burst count
    `maxScrollDepth` UInt8 DEFAULT 0, -- percentage (0-100)        
    `isVideoPlaying` Bool DEFAULT false,
    `idleDuration` UInt32 DEFAULT 0, -- probably duplicate of duration 
    `pingSequence` UInt32 DEFAULT 0,
    `lastActivityTime` DateTime DEFAULT now(),
        
    `browserType` String DEFAULT 'unknown',
    `platform` String DEFAULT 'unknown',
    `extensionVersion` String DEFAULT '',
    `createdAt` DateTime DEFAULT now(),
    `traceId` String DEFAULT '',
    
    `eventQuality` Enum8('normal' = 1, 'flushed_early' = 2, 'unload_flush' = 3) DEFAULT 'normal',
    `flushReason` String DEFAULT '', -- 'timeout', 'idle', 'unload', 'visibility_change'                
) ENGINE = ReplicatedMergeTree(
    '/clickhouse/tables/{uuid}/{multiple_shard}/interaction_summaries_local',
    '{multiple_replica}'
) 
PARTITION BY (schoolYear, toYYYYMM(startTime))
ORDER BY (
    startTime,
    userId,
    sessionId,
    tabId,
    domain,
    url,
    orgId,
    schoolId,
    role,
    grade,
    schoolYear
) 
SETTINGS index_granularity = 8192; 